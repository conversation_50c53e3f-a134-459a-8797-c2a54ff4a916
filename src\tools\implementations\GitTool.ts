/**
 * Git Tool - Git版本控制工具
 * 
 * 提供Git操作功能，如状态查询、提交、推送等
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import * as path from 'path';
import {
  ITool,
  ToolDefinition,
  ToolExecutionContext,
  ToolExecutionResult,
  ValidationResult,
  ToolUsageStats,
  GitOperation,
  GitStatus,
  ToolExecutionError,
} from '../interfaces';

const execAsync = promisify(exec);

export class GitTool implements ITool {
  public readonly definition: ToolDefinition = {
    name: 'git',
    description: 'Perform Git version control operations like status, add, commit, push, pull',
    category: 'git',
    parameters: {
      type: 'object',
      properties: {
        operation: {
          type: 'string',
          description: 'Git operation to perform',
          enum: ['status', 'add', 'commit', 'push', 'pull', 'branch', 'merge', 'diff', 'log', 'reset'],
        },
        files: {
          type: 'array',
          description: 'Files to operate on (for add, reset operations)',
          items: { type: 'string' },
        },
        message: {
          type: 'string',
          description: 'Commit message (for commit operation)',
        },
        branch: {
          type: 'string',
          description: 'Branch name (for branch, merge operations)',
        },
        remote: {
          type: 'string',
          description: 'Remote name (for push, pull operations)',
          default: 'origin',
        },
        force: {
          type: 'boolean',
          description: 'Force operation (use with caution)',
          default: false,
        },
        all: {
          type: 'boolean',
          description: 'Add all files (for add operation)',
          default: false,
        },
        limit: {
          type: 'number',
          description: 'Limit number of results (for log operation)',
          default: 10,
        },
      },
      required: ['operation'],
    },
    permissions: ['git-operations', 'execute-commands'],
    examples: [
      {
        description: 'Check Git status',
        parameters: {
          operation: 'status',
        },
      },
      {
        description: 'Add files to staging',
        parameters: {
          operation: 'add',
          files: ['src/index.ts', 'README.md'],
        },
      },
      {
        description: 'Commit changes',
        parameters: {
          operation: 'commit',
          message: 'Add new feature',
        },
      },
      {
        description: 'Push to remote',
        parameters: {
          operation: 'push',
          remote: 'origin',
          branch: 'main',
        },
      },
    ],
  };

  private usageStats: ToolUsageStats = {
    totalExecutions: 0,
    successfulExecutions: 0,
    failedExecutions: 0,
    averageExecutionTime: 0,
  };

  async execute(
    parameters: Record<string, any>,
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    this.usageStats.totalExecutions++;

    try {
      const operation = parameters.operation as string;
      const workingDir = context.workspaceRoot || process.cwd();

      let result: any;
      const commandsExecuted: string[] = [];

      switch (operation) {
        case 'status':
          result = await this.getStatus(workingDir);
          commandsExecuted.push('git status --porcelain');
          break;

        case 'add':
          result = await this.addFiles(workingDir, parameters.files, parameters.all);
          commandsExecuted.push(parameters.all ? 'git add .' : `git add ${parameters.files?.join(' ')}`);
          break;

        case 'commit':
          result = await this.commit(workingDir, parameters.message);
          commandsExecuted.push(`git commit -m "${parameters.message}"`);
          break;

        case 'push':
          result = await this.push(workingDir, parameters.remote, parameters.branch, parameters.force);
          commandsExecuted.push(`git push ${parameters.remote || 'origin'} ${parameters.branch || 'HEAD'}`);
          break;

        case 'pull':
          result = await this.pull(workingDir, parameters.remote, parameters.branch);
          commandsExecuted.push(`git pull ${parameters.remote || 'origin'} ${parameters.branch || 'HEAD'}`);
          break;

        case 'branch':
          result = await this.manageBranch(workingDir, parameters.branch, parameters.operation);
          commandsExecuted.push(`git branch ${parameters.branch || ''}`);
          break;

        case 'merge':
          result = await this.merge(workingDir, parameters.branch);
          commandsExecuted.push(`git merge ${parameters.branch}`);
          break;

        case 'diff':
          result = await this.getDiff(workingDir, parameters.files);
          commandsExecuted.push('git diff');
          break;

        case 'log':
          result = await this.getLog(workingDir, parameters.limit);
          commandsExecuted.push(`git log --oneline -${parameters.limit || 10}`);
          break;

        case 'reset':
          result = await this.reset(workingDir, parameters.files, parameters.force);
          commandsExecuted.push(`git reset ${parameters.files?.join(' ') || ''}`);
          break;

        default:
          throw new ToolExecutionError('git', `Unknown Git operation: ${operation}`);
      }

      const duration = Date.now() - startTime;
      this.updateStats(duration, true);

      return {
        success: true,
        result,
        metadata: {
          duration,
          commandsExecuted,
        },
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      this.updateStats(duration, false);

      return {
        success: false,
        error: (error as Error).message,
        metadata: { duration },
      };
    }
  }

  validate(parameters: Record<string, any>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!parameters.operation) {
      errors.push('Operation is required');
    } else if (!['status', 'add', 'commit', 'push', 'pull', 'branch', 'merge', 'diff', 'log', 'reset'].includes(parameters.operation)) {
      errors.push('Invalid Git operation');
    }

    if (parameters.operation === 'commit' && !parameters.message) {
      errors.push('Commit message is required for commit operation');
    }

    if (['merge', 'branch'].includes(parameters.operation) && !parameters.branch) {
      errors.push('Branch name is required for branch/merge operations');
    }

    if (parameters.force) {
      warnings.push('Force operation can be dangerous - use with caution');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }

  getUsageStats(): ToolUsageStats {
    return { ...this.usageStats };
  }

  // 私有方法
  private async executeGitCommand(command: string, cwd: string): Promise<string> {
    try {
      const { stdout, stderr } = await execAsync(command, { cwd });
      if (stderr && !stderr.includes('warning')) {
        throw new Error(stderr);
      }
      return stdout.trim();
    } catch (error) {
      throw new ToolExecutionError('git', `Git command failed: ${(error as Error).message}`);
    }
  }

  private async getStatus(workingDir: string): Promise<GitStatus> {
    try {
      // 获取当前分支
      const branchOutput = await this.executeGitCommand('git branch --show-current', workingDir);
      const currentBranch = branchOutput || 'HEAD';

      // 获取状态
      const statusOutput = await this.executeGitCommand('git status --porcelain', workingDir);
      
      // 获取远程状态
      let ahead = 0;
      let behind = 0;
      try {
        const revListOutput = await this.executeGitCommand(
          `git rev-list --left-right --count origin/${currentBranch}...HEAD`,
          workingDir
        );
        const [behindStr, aheadStr] = revListOutput.split('\t');
        behind = parseInt(behindStr) || 0;
        ahead = parseInt(aheadStr) || 0;
      } catch {
        // 忽略远程分支不存在的错误
      }

      // 解析文件状态
      const staged: string[] = [];
      const unstaged: string[] = [];
      const untracked: string[] = [];
      const conflicts: string[] = [];

      statusOutput.split('\n').forEach(line => {
        if (line.length < 3) return;
        
        const status = line.substring(0, 2);
        const file = line.substring(3);

        if (status.includes('U') || status.includes('A') && status.includes('A')) {
          conflicts.push(file);
        } else if (status[0] !== ' ' && status[0] !== '?') {
          staged.push(file);
        } else if (status[1] !== ' ') {
          unstaged.push(file);
        } else if (status === '??') {
          untracked.push(file);
        }
      });

      return {
        branch: currentBranch,
        ahead,
        behind,
        staged,
        unstaged,
        untracked,
        conflicts,
      };
    } catch (error) {
      throw new ToolExecutionError('git', `Failed to get Git status: ${(error as Error).message}`);
    }
  }

  private async addFiles(workingDir: string, files?: string[], all?: boolean): Promise<any> {
    let command = 'git add';
    
    if (all) {
      command += ' .';
    } else if (files && files.length > 0) {
      command += ' ' + files.map(f => `"${f}"`).join(' ');
    } else {
      throw new ToolExecutionError('git', 'No files specified for add operation');
    }

    await this.executeGitCommand(command, workingDir);
    return { success: true, filesAdded: files || ['all'] };
  }

  private async commit(workingDir: string, message: string): Promise<any> {
    if (!message) {
      throw new ToolExecutionError('git', 'Commit message is required');
    }

    const command = `git commit -m "${message.replace(/"/g, '\\"')}"`;
    const output = await this.executeGitCommand(command, workingDir);
    
    // 解析提交信息
    const commitMatch = output.match(/\[([^\]]+)\s+([a-f0-9]+)\]/);
    const branch = commitMatch?.[1] || 'unknown';
    const hash = commitMatch?.[2] || 'unknown';

    return {
      success: true,
      branch,
      hash,
      message,
      output,
    };
  }

  private async push(workingDir: string, remote?: string, branch?: string, force?: boolean): Promise<any> {
    let command = `git push ${remote || 'origin'}`;
    
    if (branch) {
      command += ` ${branch}`;
    }
    
    if (force) {
      command += ' --force';
    }

    const output = await this.executeGitCommand(command, workingDir);
    return { success: true, output };
  }

  private async pull(workingDir: string, remote?: string, branch?: string): Promise<any> {
    let command = `git pull ${remote || 'origin'}`;
    
    if (branch) {
      command += ` ${branch}`;
    }

    const output = await this.executeGitCommand(command, workingDir);
    return { success: true, output };
  }

  private async manageBranch(workingDir: string, branchName?: string, operation?: string): Promise<any> {
    if (!branchName) {
      // 列出分支
      const output = await this.executeGitCommand('git branch -a', workingDir);
      const branches = output.split('\n').map(line => line.trim().replace(/^\*\s*/, ''));
      return { branches };
    }

    // 创建或切换分支
    const command = `git checkout -b ${branchName}`;
    const output = await this.executeGitCommand(command, workingDir);
    return { success: true, branch: branchName, output };
  }

  private async merge(workingDir: string, branch: string): Promise<any> {
    const command = `git merge ${branch}`;
    const output = await this.executeGitCommand(command, workingDir);
    return { success: true, merged: branch, output };
  }

  private async getDiff(workingDir: string, files?: string[]): Promise<any> {
    let command = 'git diff';
    
    if (files && files.length > 0) {
      command += ' ' + files.map(f => `"${f}"`).join(' ');
    }

    const output = await this.executeGitCommand(command, workingDir);
    return { diff: output };
  }

  private async getLog(workingDir: string, limit: number = 10): Promise<any> {
    const command = `git log --oneline -${limit}`;
    const output = await this.executeGitCommand(command, workingDir);
    
    const commits = output.split('\n').map(line => {
      const [hash, ...messageParts] = line.split(' ');
      return {
        hash,
        message: messageParts.join(' '),
      };
    });

    return { commits };
  }

  private async reset(workingDir: string, files?: string[], hard?: boolean): Promise<any> {
    let command = 'git reset';
    
    if (hard) {
      command += ' --hard';
    }
    
    if (files && files.length > 0) {
      command += ' ' + files.map(f => `"${f}"`).join(' ');
    }

    const output = await this.executeGitCommand(command, workingDir);
    return { success: true, output };
  }

  private updateStats(duration: number, success: boolean): void {
    if (success) {
      this.usageStats.successfulExecutions++;
    } else {
      this.usageStats.failedExecutions++;
    }

    const totalTime = this.usageStats.averageExecutionTime * (this.usageStats.totalExecutions - 1) + duration;
    this.usageStats.averageExecutionTime = totalTime / this.usageStats.totalExecutions;
    this.usageStats.lastUsed = Date.now();
  }
}
